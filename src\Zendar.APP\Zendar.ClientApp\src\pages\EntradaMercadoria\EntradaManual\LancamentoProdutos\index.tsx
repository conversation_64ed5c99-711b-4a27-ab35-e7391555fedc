import { But<PERSON>, Box, Icon, Stack, useMediaQuery } from '@chakra-ui/react';
import { useCallback, useState, useMemo, useEffect, useRef } from 'react';
import { toast } from 'react-toastify';

import {
  buscarVariacoesProduto,
  CorOption,
  TamanhoOption,
} from 'helpers/api/Produto';
import { formatQueryPagegTable } from 'helpers/format/formatQueryParams';
import useWindowSize from 'helpers/layout/useWindowSize';

import api, { ResponseApi } from 'services/api';

import { useEntradaMercadoriaDadosCadastroContext } from 'store/EntradaMercadoria/EntradaMercadoriaDadosCadastro';
import { useEntradaMercadoriaEtapasContext } from 'store/EntradaMercadoria/EntradaMercadoriaEtapas';
import { usePadronizacaoContext } from 'store/Padronizacao/Padronizacao';

import TotalizadoresFixos from 'pages/EntradaMercadoria/Importacao/Continuar/VincularProdutos/components/TotalizadoresFixos';

import LoadingPadrao from 'components/Layout/Loading/LoadingPadrao';
import { ModalConfirmacaoExcluir } from 'components/Modal/ModalConfirmacaoExcluir';
import {
  Container,
  Body,
  Footer,
  StepDescriptionAccordion,
} from 'components/update/Steps/StepContent';
import {
  LoadMoreRowsParams,
  TableHeader,
  ForwardRefData,
} from 'components/update/Table/VirtualizedInfiniteTable';

import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';
import { SalvarInserirNovoIcon } from 'icons';

import { ListagemLancamentoManual } from './components/ListagemLancamentoManual';
import { ModalAdicionarProduto } from './ModalAdicionarProduto';
import { ModalEditarProduto } from './ModalEditarProduto';
import {
  ObterInformacoesProdutosResponse,
  Produto,
  ProdutoPaginadoRetorno,
  InformacoesRodape,
  ObterProdutosDaListaProps,
  ProdutoResponse,
  AdicionarProdutoRetorno,
  ProdutoObterResponse,
} from './types';

export function LancamentoProdutos() {
  const { casasDecimais } = usePadronizacaoContext();
  const { nextStep, previousStep } = useEntradaMercadoriaEtapasContext();
  const {
    entradaMercadoriaId,
    descartarEntradaMercadoria,
    voltarParaListagem,
    temPermissaoExcluir,
    isReadOnly,
    IsCadastroExterno,
    statusLancamentos: { foiLancadoEstoque, foiLancadoFinanceiro },
    menuIsOpen,
  } = useEntradaMercadoriaDadosCadastroContext();

  const virtualizedInfiniteTableRef = useRef<ForwardRefData>(null);

  const [isLoading, setIsLoading] = useState(false);
  const [entradaRateiaIcmsSt, setEntradaRateiaIcmsSt] = useState(false);
  const [produtos, setProdutos] = useState<Produto[]>([]);
  const [informacoesRodape, setInformacoesRodape] = useState<InformacoesRodape>(
    { quantidadeItens: 0, totalProdutos: 0, valorTotalProdutos: 0 }
  );
  const [isLargerThan900] = useMediaQuery('(min-width: 900px)');
  const { height: windowHeight } = useWindowSize();

  const maxContainerHeight = useMemo((): string => {
    const stepDescriptionHeight = 80;
    const containerPadding = 48;
    const totalizadoresHeight = informacoesRodape.totalProdutos > 0 ? 155 : 0;
    const footerHeight = isLargerThan900 ? 70 : 0;
    const marginBottom = 24;

    const availableHeight =
      windowHeight -
      stepDescriptionHeight -
      containerPadding -
      totalizadoresHeight -
      footerHeight -
      marginBottom;

    const dynamicHeight = Math.max(availableHeight, 300);

    return `${dynamicHeight}px`;
  }, [windowHeight, isLargerThan900, informacoesRodape.totalProdutos]);

  function handleToggleLinhaProduto(index: number) {
    const produtosAtualizados = [...produtos];
    const produtoAtualizado = produtosAtualizados[index];

    produtosAtualizados.splice(index, 1, {
      ...produtoAtualizado,
      isOpen: !produtoAtualizado.isOpen,
    });

    setProdutos(produtosAtualizados);
  }

  const isLoadingMoreRows = useRef(false);

  const loadMoreRows = useCallback(
    async ({
      currentPage,
      pageSize,
      orderColumn,
      orderDirection,
    }: ObterProdutosDaListaProps) => {
      if (entradaMercadoriaId) {
        setIsLoading(true);
        const paginationData = {
          currentPage,
          pageSize,
          orderColumn,
          orderDirection,
        };

        const response = await api.get<
          void,
          ResponseApi<ProdutoPaginadoRetorno>
        >(
          formatQueryPagegTable(
            ConstanteEnderecoWebservice.ENTRADA_MERCADORIA_LISTAR_ITENS_PAGINADOS,
            paginationData
          ),
          { params: { id: entradaMercadoriaId } }
        );

        if (response) {
          if (response.sucesso) {
            setProdutos(
              (response.dados.registros || []).map(
                (registro) => ({ ...registro, isOpen: false } as Produto)
              )
            );

            setInformacoesRodape({
              quantidadeItens: response.dados.totalItens,
              totalProdutos: response.dados.totalProdutos,
              valorTotalProdutos: response.dados.valorTotal,
            });
          }
        }
      }

      setIsLoading(false);
    },
    [entradaMercadoriaId]
  );

  const handleDescartarEntradaMercadoria = async () => {
    setIsLoading(true);
    await descartarEntradaMercadoria();
    setIsLoading(false);
  };

  const handleLoadMoreRows = async (
    params: LoadMoreRowsParams
  ): Promise<void> => {
    if (isLoadingMoreRows.current) {
      return;
    }

    isLoadingMoreRows.current = true;
    await loadMoreRows({
      ...params,
    });
    isLoadingMoreRows.current = false;
  };

  const podeAlteraItens = foiLancadoEstoque || foiLancadoFinanceiro;

  const adicionarProduto = useCallback(
    async (produtoSendoAdicionado: ProdutoResponse) => {
      setIsLoading(true);
      const response = await api.post<
        void,
        ResponseApi<AdicionarProdutoRetorno[]>
      >(ConstanteEnderecoWebservice.ENTRADA_MERCADORIA_ADICIONAR_ITENS, {
        entradaMercadoriaId,
        ...produtoSendoAdicionado,
      });

      if (response) {
        if (response.avisos) {
          response.avisos.map((aviso: string) => toast.warning(aviso));
        }

        if (response.sucesso) {
          if (virtualizedInfiniteTableRef?.current) {
            await virtualizedInfiniteTableRef.current.reload();
          }

          setIsLoading(false);
        }
      }
      setIsLoading(false);
    },
    [entradaMercadoriaId]
  );

  const handleAdicionarProduto = useCallback(async () => {
    if (entradaMercadoriaId) {
      setIsLoading(true);
      try {
        const { produto: produtoInformadoNoModal, confirmarAdicionarOutro } =
          await ModalAdicionarProduto({
            casasDecimaisQuantidade: casasDecimais.casasDecimaisQuantidade,
            casasDecimaisValor: casasDecimais.casasDecimaisValor,
            entradaRateiaIcmsSt,
            adicionarProduto,
          });

        if (produtoInformadoNoModal) {
          await adicionarProduto(produtoInformadoNoModal);
        }

        if (confirmarAdicionarOutro) {
          handleAdicionarProduto();
        }
        setIsLoading(false);
      } catch (error) {
        setIsLoading(false);
      }
    }
  }, [
    adicionarProduto,
    casasDecimais,
    entradaMercadoriaId,
    entradaRateiaIcmsSt,
  ]);

  async function handleEditarProduto(index: number) {
    if (!entradaMercadoriaId) return;
    const entradaMercadoriaItemId = produtos[index]?.entradaMercadoriaItemId;
    const url =
      ConstanteEnderecoWebservice.ENTRADA_MERCADORIA_OBTER_ITEM.replace(
        '{entradaMercadoriaItemId}',
        entradaMercadoriaItemId
      ).replace('{entradaMercadoriaId}', entradaMercadoriaId);

    const responseObter = await api.get<
      void,
      ResponseApi<ProdutoObterResponse>
    >(url);
    if (responseObter) {
      if (responseObter.avisos) {
        responseObter.avisos.forEach((aviso: string) => toast.warning(aviso));
        return;
      }
      if (responseObter.sucesso && responseObter.dados) {
        const produtoParaEdicao = responseObter.dados;
        const produtoTipoSimples =
          produtoParaEdicao.variacoes.length === 1 &&
          produtoParaEdicao.variacoes[0].corId === null &&
          produtoParaEdicao.variacoes[0].tamanhoId === null;

        let coresProduto = [] as CorOption[];
        let tamanhosProduto = [] as TamanhoOption[];

        if (!produtoTipoSimples) {
          const { cores, tamanhos } = await buscarVariacoesProduto(
            produtoParaEdicao.variacoes[0].produtoId
          );
          coresProduto = cores;
          tamanhosProduto = tamanhos;
        }

        const { produtoEditado } = await ModalEditarProduto({
          nomeProduto: produtoParaEdicao?.variacoes[0]?.nome,
          produtoId: produtoParaEdicao?.variacoes[0]?.produtoId,
          produtoTipoVariacao: !produtoTipoSimples,
          variacoes: produtoParaEdicao.variacoes,
          casasDecimaisQuantidade: casasDecimais.casasDecimaisQuantidade,
          casasDecimaisValor: casasDecimais.casasDecimaisValor,
          volumeUnitario: produtoParaEdicao.volumeUnitario,
          dadosEntrada: {
            custoAdicional: produtoParaEdicao.custoAdicional,
            fcpSt: produtoParaEdicao.valorFcpSt,
            icmsSt: produtoParaEdicao.valorIcmsSt,
            ipi: produtoParaEdicao.valorIpi,
            valorUnitario: produtoParaEdicao.valorUnitarioEntrada,
            quantidade: produtoTipoSimples
              ? produtoParaEdicao.quantidadeEntrada
              : 1,
          },
          entradaRateiaIcmsSt,
          cores: coresProduto,
          tamanhos: tamanhosProduto,
        });

        if (produtoEditado) {
          const response = await api.put<void, ResponseApi>(
            ConstanteEnderecoWebservice.ENTRADA_MERCADORIA_ALTERAR_ITEM,
            {
              entradaMercadoriaItemId,
              entradaMercadoriaId,
              valorUnitarioEntrada: produtoEditado.valorUnitarioEntrada,
              valorIpi: produtoEditado.valorIpi,
              valorIcmsSt: produtoEditado.valorIcmsSt,
              valorFcpSt: produtoEditado.valorFcpSt,
              custoAdicional: produtoEditado.custoAdicional,
              produtoId: produtoEditado.produtoId,
              variacoes: produtoEditado.variacoes,
            }
          );

          if (response) {
            if (response.avisos) {
              response.avisos.forEach((aviso: string) => toast.warning(aviso));
            }

            if (response.sucesso) {
              if (virtualizedInfiniteTableRef?.current) {
                await virtualizedInfiniteTableRef.current.reload();
              }
            }
          }
        }
      }
    }
  }

  async function handleRemoverProduto(index: number) {
    ModalConfirmacaoExcluir({
      title: 'Remover produto',
      text: 'Você tem certeza que deseja remover este produto da sua entrada manual?',
      submitText: 'Sim, remover',
      cancelText: 'Voltar',
      callback: async (ok: boolean) => {
        if (ok) {
          const { quantidade, entradaMercadoriaItemId, valorTotal } =
            produtos[index];

          if (entradaMercadoriaItemId && entradaMercadoriaId) {
            const url =
              ConstanteEnderecoWebservice.ENTRADA_MERCADORIA_REMOVER_ITEM.replace(
                '{entradaMercadoriaId}',
                entradaMercadoriaId
              ).replace('{entradaMercadoriaItemId}', entradaMercadoriaItemId);
            const response = await api.delete<void, ResponseApi>(url);

            if (response) {
              if (response.avisos) {
                response.avisos.map((aviso: string) => toast.warning(aviso));
              }

              if (response.sucesso) {
                const newProdutos = [...produtos];

                newProdutos.splice(index, 1);

                setProdutos(newProdutos);

                setInformacoesRodape((prev) => ({
                  totalProdutos: prev.totalProdutos - 1,
                  quantidadeItens: prev.quantidadeItens - quantidade,
                  valorTotalProdutos: prev.valorTotalProdutos - valorTotal,
                }));
              }
            }
          }
        }
      },
    });
  }

  const produtosTableHeaders: TableHeader[] = useMemo(
    () => [
      {
        key: 'produto',
        content: podeAlteraItens ? (
          'Produto'
        ) : (
          <Button
            size="xs"
            borderRadius="full"
            colorScheme="secondary"
            leftIcon={<Icon as={SalvarInserirNovoIcon} fontSize="sm" />}
            minW="164px"
            height="32px"
            fontSize="sm"
            fontStyle="italic"
            onClick={handleAdicionarProduto}
            cursor="pointer"
            style={{ marginLeft: '2px' }}
          >
            Adicionar itens
          </Button>
        ),
        paddingLeft: podeAlteraItens ? '48px !important' : '0px !important',
        width: '70%',
        minWidth: '70%',
      },
      {
        key: 'quantidade',
        content: 'Quantidade',
        isNumeric: false,
        width: '10%',
        minWidth: '10%',
        lineHeight: 'none',
        verticalAlign: 'bottom',
      },
      {
        key: 'valorUnitario',
        content: 'Valor unitário',
        width: '10%',
        minWidth: '10%',
        lineHeight: 'none',
        verticalAlign: 'bottom',
        isNumeric: true,
      },
      {
        key: 'valorTotal',
        content: 'Valor total',
        width: '10%',
        minWidth: '10%',
        lineHeight: 'none',
        verticalAlign: 'bottom',
        isNumeric: true,
      },
      {
        key: 'acoes',
        width: '180px',
        minWidth: '180px',
        content: 'Ações',
        textAlign: 'end',
        lineHeight: 'none',
        verticalAlign: 'bottom',
      },
    ],
    [handleAdicionarProduto, podeAlteraItens]
  );

  const getDynamicHeight = (index: number, marginSize: number): number => {
    const produto = produtos[index];
    if (!produto) return 56 + marginSize;

    const isLastItem = index === produtos.length - 1;
    const closedProdutoHeight = 56 + (isLastItem ? 0 : marginSize);
    const openedProdutoHeight = 108 + (isLastItem ? 0 : marginSize);
    const produtoHeight = produto.isOpen
      ? openedProdutoHeight
      : closedProdutoHeight;
    return produtoHeight;
  };

  useEffect(() => {
    async function obterInformacoesProdutos() {
      if (entradaMercadoriaId) {
        setIsLoading(true);

        const response = await api.get<
          void,
          ResponseApi<ObterInformacoesProdutosResponse>
        >(
          ConstanteEnderecoWebservice.ENTRADA_MERCADORIA_OBTER_INFORMACOES_PRODUTOS,
          {
            params: {
              id: entradaMercadoriaId,
            },
          }
        );

        if (response) {
          if (response.avisos) {
            response.avisos.map((aviso: string) => toast.warning(aviso));
          }

          if (response.sucesso && response.dados) {
            setEntradaRateiaIcmsSt(response.dados.ratearIcmsSt);
          }
        }

        setIsLoading(false);
      }
    }

    obterInformacoesProdutos();
  }, [entradaMercadoriaId]);

  return (
    <>
      {isLoading && <LoadingPadrao />}

      <Container mt="6px">
        <StepDescriptionAccordion
          stepNumber={2}
          title="Lista de produtos"
          description="Clique em “adicionar itens” para formar a lista de produtos. Caso exista um novo produto será preciso cadastrá-lo na própria tela de seleção."
        />

        <Body>
          <Box
            display="flex"
            flexDirection="column"
            justifyContent="space-between"
            borderRadius="md"
            border="1px"
            bg="gray.50"
            borderColor="gray.200"
            maxH={maxContainerHeight}
            py={{ base: 4, sm: 6, md: 6 }}
            pl={{ base: 4, sm: 6, md: 6 }}
            pr={{ base: '6px', sm: '14px', md: '24px' }}
            pt={{ base: 4, sm: '16px', md: '16px' }}
            sx={{
              '& table': { bg: 'gray.50' },
              '& thead > tr > th': {
                bg: 'gray.50',
                border: 'none',
              },
              '& td:first-of-type': {
                paddingLeft: '16px !important',
              },
              '& tbody > tr': {
                borderRadius: 'md',
                boxShadow: '0px 0px 2px #00000029',
                ...(informacoesRodape.totalProdutos > 0
                  ? {
                      border: '1px',
                      borderColor: 'gray.100',
                    }
                  : {
                      '& > td': {
                        position: 'relative',
                        _before: {
                          content: '""',
                          position: 'absolute',
                          h: 'full',
                          w: 'full',
                          top: 0,
                          left: 0,
                          borderLeft: 'none',
                          borderRight: 'none',
                          borderRadius: 'md',
                        },
                      },
                    }),
              },
              '& tbody > tr > td': {
                bg: 'white',
                lineHeight: 'none',
                _before: {
                  border:
                    informacoesRodape.totalProdutos > 0
                      ? 'none !important'
                      : '1px',
                  borderColor: 'gray.100',
                },
              },
            }}
          >
            <ListagemLancamentoManual
              produtos={produtos}
              informacoesRodape={informacoesRodape}
              handleLoadMoreRows={handleLoadMoreRows}
              handleToggleLinhaProduto={handleToggleLinhaProduto}
              handleEditarProduto={handleEditarProduto}
              handleRemoverProduto={handleRemoverProduto}
              foiLancadoEstoque={foiLancadoEstoque}
              isReadOnly={isReadOnly}
              casasDecimais={casasDecimais}
              virtualizedInfiniteTableRef={virtualizedInfiniteTableRef}
              getDynamicHeight={getDynamicHeight}
              produtosTableHeaders={produtosTableHeaders}
            />
          </Box>
        </Body>
      </Container>
      {informacoesRodape.totalProdutos > 0 && (
        <TotalizadoresFixos
          quantidadeItens={informacoesRodape.quantidadeItens}
          totalProdutos={informacoesRodape.totalProdutos}
          valorTotalProdutos={informacoesRodape.valorTotalProdutos}
        />
      )}
      <Footer
        justifyContent="space-between"
        position={isLargerThan900 ? 'fixed' : 'relative'}
        bottom="0px"
        bg="gray.50"
        borderTop={isLargerThan900 ? '1px solid' : 'none'}
        borderColor="#5502B2"
        w={`calc(100% - ${menuIsOpen ? '210px' : '108px'})`}
        py="16px"
        px="48px"
      >
        <Button
          variant="outlineDefault"
          borderRadius="full"
          w="full"
          maxW={{ base: 'full', md: '160px' }}
          onClick={previousStep}
        >
          Voltar
        </Button>
        <Stack
          w="full"
          justifyContent="flex-end"
          direction={{ base: 'column', md: 'row' }}
          spacing={{ base: 2, sm: 4, md: 6 }}
        >
          {!IsCadastroExterno && (
            <>
              {isReadOnly ? (
                <Button
                  variant="outlineDefault"
                  borderRadius="full"
                  w="full"
                  maxW={{ base: 'full', md: '196px' }}
                  onClick={voltarParaListagem}
                >
                  Voltar para a listagem
                </Button>
              ) : (
                <Button
                  variant="outlineDefault"
                  borderRadius="full"
                  w="full"
                  maxW={{ base: 'full', md: '160px' }}
                  onClick={handleDescartarEntradaMercadoria}
                  isDisabled={!temPermissaoExcluir}
                >
                  Descartar
                </Button>
              )}
            </>
          )}

          {!isReadOnly && (
            <Button
              variant="outlineDefault"
              borderRadius="full"
              w="full"
              maxW={{ base: 'full', md: '160px' }}
              onClick={voltarParaListagem}
            >
              Salvar e sair
            </Button>
          )}

          <Button
            colorScheme="purple"
            borderRadius="full"
            w="full"
            maxW={{ base: 'full', md: '160px' }}
            onClick={nextStep}
            isDisabled={produtos.length === 0}
          >
            Avançar
          </Button>
        </Stack>
      </Footer>
    </>
  );
}
