import {
  Tr,
  Td,
  Flex,
  Icon,
  Box,
  Divider,
  HStack,
  Text,
  Button,
} from '@chakra-ui/react';
import React from 'react';
import { FiChevronUp } from 'react-icons/fi';
import { Index, CellMeasurer, CellMeasurerCache } from 'react-virtualized';

import { DecimalMask } from 'helpers/format/fieldsMasks';

import { ActionsMenu } from 'components/update/Table/ActionsMenu';
import {
  ForwardRefData,
  LoadMoreRowsParams,
  TableHeader,
  VirtualizedInfiniteTable,
} from 'components/update/Table/VirtualizedInfiniteTable';
import { InfoTooltip } from 'components/update/Tooltip/InfoTooltip';

import { Produto, InformacoesRodape } from '../types';

type ListagemLancamentoManualProps = {
  produtos: Produto[];
  informacoesRodape: InformacoesRodape;
  handleLoadMoreRows: (params: LoadMoreRowsParams) => Promise<void>;
  handleToggleLinhaProduto: (index: number) => void;
  handleEditarProduto: (index: number) => void;
  handleRemoverProduto: (index: number) => void;
  foiLancadoEstoque: boolean;
  isReadOnly: boolean;
  casasDecimais: {
    casasDecimaisQuantidade: number;
    casasDecimaisValor: number;
  };
  virtualizedInfiniteTableRef: React.RefObject<ForwardRefData>;
  getDynamicHeight: (index: number, produtoHeight: number) => number;
  getFixedTableHeight: number;
  produtosTableHeaders: TableHeader[];
};

const cache = new CellMeasurerCache({
  defaultHeight: 65,
  minHeight: 52,
  fixedWidth: true,
});

export const ListagemLancamentoManual = ({
  produtos,
  informacoesRodape,
  handleLoadMoreRows,
  handleToggleLinhaProduto,
  handleEditarProduto,
  handleRemoverProduto,
  getDynamicHeight,
  foiLancadoEstoque,
  isReadOnly,
  casasDecimais,
  virtualizedInfiniteTableRef,
  getFixedTableHeight,
  produtosTableHeaders,
}: ListagemLancamentoManualProps) => {
  return (
    <VirtualizedInfiniteTable
      variant="simple-card"
      size="sm"
      bg="gray.50"
      boxShadow="none"
      withoutRowsMessage="Nenhum produto adicionado."
      orderColumn="nomeProduto"
      tableHeaders={produtosTableHeaders}
      itemHeight={54}
      degradeSuperior="linear-gradient(to bottom, #f5f5f5 0%, rgb(255 255 255 / 30%) 100%)"
      degradeInferior="linear-gradient(to top, #f5f5f5 0%, rgb(245 245 245 / 30%) 100%)"
      ref={virtualizedInfiniteTableRef}
      colorFundo="gray.50"
      visibleItemsCount={9}
      dynamicHeight={({ index }: Index) => getDynamicHeight(index, 5)}
      rowCount={informacoesRodape.totalProdutos}
      isRowLoaded={({ index }: Index) => !!produtos[index]}
      loadMoreRows={handleLoadMoreRows}
      heightTable={getFixedTableHeight}
      pageSize={25}
      rowRenderer={({
        index,
        style: { height, ...restStyle },
        key,
        parent,
      }) => {
        const produto = produtos[index];

        if (!produto) {
          return null;
        }

        return (
          <CellMeasurer
            cache={cache}
            columnIndex={1}
            key={key}
            parent={parent}
            rowIndex={index}
          >
            {({ registerChild, measure }) => (
              <Tr
                transition="all 0.3s"
                ref={(e) => {
                  if (e && registerChild) {
                    registerChild(e);
                  }
                }}
                style={restStyle}
                h={`${getDynamicHeight(index, 0)}px !important`}
                sx={
                  produto.isOpen
                    ? {
                        '& > td': {
                          marginBottom: '5px',
                          borderBottomRadius: '0px !important',
                        },
                      }
                    : {}
                }
              >
                <Td
                  width={produtosTableHeaders[0].width}
                  cursor="pointer"
                  userSelect="none"
                  fontSize="14px"
                  onClick={() => {
                    measure();
                    handleToggleLinhaProduto(index);
                  }}
                >
                  <Flex alignItems="center" gap="8px">
                    <Button
                      tabIndex={0}
                      bg="transparent"
                      p="4px"
                      pb="0px"
                      mr="6px"
                      h="fit-content"
                      borderRadius="6px"
                      _focus={{
                        background: 'gray.100',
                      }}
                      minW="16px"
                    >
                      <Icon
                        as={FiChevronUp}
                        mb="6px"
                        transform={produto.isOpen ? '' : 'rotate(180deg)'}
                        role="button"
                        transition="all 0.3s"
                      />
                    </Button>

                    <Text overflowWrap="anywhere" noOfLines={2}>
                      {produto.nomeProduto}
                    </Text>
                  </Flex>
                </Td>
                <Td
                  width={produtosTableHeaders[1].width}
                  minWidth={produtosTableHeaders[1].width}
                  fontSize="14px"
                >
                  {DecimalMask(
                    produto.quantidade,
                    casasDecimais.casasDecimaisQuantidade
                  )}
                </Td>
                <Td
                  width={produtosTableHeaders[2].width}
                  minWidth={produtosTableHeaders[2].width}
                  isNumeric
                  fontSize="14px"
                >
                  {DecimalMask(
                    produto.valorUnitarioEntrada,
                    casasDecimais.casasDecimaisValor
                  )}
                </Td>
                <Td
                  width={produtosTableHeaders[3].width}
                  minWidth={produtosTableHeaders[3].width}
                  isNumeric
                  fontSize="14px"
                >
                  {DecimalMask(produto.valorTotal, 2, 2)}
                </Td>
                <Td
                  width={produtosTableHeaders[4].width}
                  minWidth={produtosTableHeaders[4].width}
                >
                  <Flex justify="end">
                    <ActionsMenu
                      isDisabled={
                        foiLancadoEstoque ||
                        isReadOnly ||
                        produto.bloquearAlteracao
                      }
                      items={[
                        {
                          content: 'Editar',
                          onClick: () => {
                            handleEditarProduto(index);
                          },
                        },
                        {
                          content: 'Remover',
                          onClick: () => {
                            handleRemoverProduto(index);
                          },
                        },
                      ]}
                    />
                  </Flex>
                </Td>

                {produto.isOpen && (
                  <Box
                    h="52px"
                    borderBottomRadius="md"
                    bg="white"
                    px="5"
                    transition="all 0.3s"
                  >
                    <Divider />

                    <HStack
                      spacing="6"
                      px="5"
                      pl="26px"
                      h="full"
                      lineHeight="none"
                      fontSize="xs"
                    >
                      <Flex>
                        <Text fontWeight="light">ICMS ST:</Text>
                        <Text ml="2" fontWeight="bold">
                          <Text as="span" fontSize="2xs" mr="0.5">
                            R$
                          </Text>
                          {DecimalMask(produto.valorIcmsSt, 2, 2)}
                        </Text>
                      </Flex>
                      <Divider orientation="vertical" h="6" />
                      <Flex>
                        <Text fontWeight="light">IPI:</Text>
                        <Text ml="2" fontWeight="bold">
                          <Text as="span" fontSize="2xs" mr="0.5">
                            R$
                          </Text>
                          {DecimalMask(produto.valorIpi, 2, 2)}
                        </Text>
                      </Flex>
                      <Divider orientation="vertical" h="6" />
                      <Flex>
                        <Text fontWeight="light">FCP ST:</Text>
                        <Text ml="2" fontWeight="bold">
                          <Text as="span" fontSize="2xs" mr="0.5">
                            R$
                          </Text>
                          {DecimalMask(produto.valorFcpSt, 2, 2)}
                        </Text>
                      </Flex>
                      <Divider orientation="vertical" h="6" />
                      <Flex h="center" alignItems="center">
                        <Text fontWeight="light">Custo adicional:</Text>
                        <Text ml="2" fontWeight="bold">
                          <Text as="span" fontSize="2xs" mr="0.5">
                            R$
                          </Text>
                          {DecimalMask(produto.custoAdicional, 2, 2)}
                        </Text>

                        <Box ml="2">
                          <InfoTooltip
                            label="Os valores deste campo não serão somados ao valor total da entrada, servindo apenas para compor o custo do produto."
                            tabIndex={-1}
                          />
                        </Box>
                      </Flex>
                    </HStack>
                  </Box>
                )}
              </Tr>
            )}
          </CellMeasurer>
        );
      }}
    />
  );
};
